<script setup lang="ts">
import { useDictItem } from '@/hooks/useDictItem'
import WarehouseTab from './components/WarehouseTab/index.vue'
import ChannelTab from './components/ChannelTab/index.vue'
import CustomerTab from './components/CustomerTab/index.vue'

defineOptions({
  name: 'ChannelEntity'
})

const warehouse_type = useDictItem('warehouse_type')
const service_provider_type = useDictItem('service_provider_type')
const weight_mode = useDictItem('weight_mode')
const charge_mode = useDictItem('charge_mode')

provide('warehouse_type', warehouse_type)
provide('service_provider_type', service_provider_type)
provide('weight_mode', weight_mode)
provide('charge_mode', charge_mode)

const active = ref('1')

const formData = ref({
  transportServiceName: undefined,
  transportServiceCode: undefined,
  chargeMode: undefined,
  weightMode: undefined,
  arrivalCountry: undefined,
  destinationType: '',
  name: undefined as any
})
</script>

<template>
  <ContentWrap>
    <el-form
      :model="formData"
      ref="formDataRef"
      label-position="left"
      @submit.prevent
    >
      <el-row :gutter="16">
        <el-col :span="8">
          <el-form-item label="渠道类型" prop="">
            <DSelect
              v-model="formData.chargeMode"
              :options="charge_mode"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="物流渠道代码" prop="">
            <el-input
              v-model.trim="formData.chargeMode"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="物流渠道名称" prop="">
            <el-input
              v-model.trim="formData.chargeMode"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="承运商" prop="">
            <DSelect
              v-model="formData.weightMode"
              :options="weight_mode"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="保险服务" prop="">
            <el-radio-group v-model="formData.name">
              <el-radio :label="'支持保险'" :value="1"></el-radio>
              <el-radio :label="'不支持保险'" :value="2"></el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="签名服务" prop="">
            <el-checkbox-group v-model="formData.name">
              <el-checkbox label="直接签名" :value="1" />
              <el-checkbox label="间接签名" :value="2" />
              <el-checkbox label="成人签名" :value="3" />
            </el-checkbox-group>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="上传物流面单" prop="">
            <el-radio-group v-model="formData.name">
              <el-radio :label="'不需要'" :value="1"></el-radio>
              <el-radio :label="'需要'" :value="2"></el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col :span="8" v-if="formData.name === 2">
          <el-form-item label="平台面单" prop="">
            <el-radio-group v-model="formData.name">
              <el-radio :label="'获取'" :value="1"></el-radio>
              <el-radio :label="'不获取'" :value="2"></el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col :span="8" v-if="formData.name === 2">
          <el-form-item label="客户上传面单" prop="">
            <el-radio-group v-model="formData.name">
              <el-radio :label="'支持'" :value="1"></el-radio>
              <el-radio :label="'不支持'" :value="2"></el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ContentWrap>

  <ContentWrap class="mt-20">
    <el-tabs v-model="active" class="mb-16">
      <el-tab-pane label="绑定仓库" name="1">
        <WarehouseTab />
      </el-tab-pane>
      <el-tab-pane label="绑定渠道" name="2">
        <ChannelTab />
      </el-tab-pane>
      <el-tab-pane label="绑定客户" name="3">
        <CustomerTab />
      </el-tab-pane>
    </el-tabs>
  </ContentWrap>
</template>

<style lang="scss" scoped></style>
