<script setup lang="ts">
import { useTable } from '@/hooks/useTable'
import { useDictItem } from '@/hooks/useDictItem'
import {
  deleteFirstVesselSetting,
  getFirstVesselSettingList
} from '@/api/initialServiceSettings'
import type { FirstVesselSettingEntity } from '@/api/initialServiceSettings/types'
import AddWarehouse from './components/AddWarehouse.vue'
import AddChannel from './components/AddChannel.vue'
import AddCustomer from './components/AddCustomer.vue'
import { useToggle } from '@/hooks/useToggle'

defineOptions({
  name: 'ChannelEntity'
})

const warehouse_type = useDictItem('warehouse_type')
const service_provider_type = useDictItem('service_provider_type')

const weight_mode = useDictItem('weight_mode')
const charge_mode = useDictItem('charge_mode')

provide('warehouse_type', warehouse_type)

provide('service_provider_type', service_provider_type)

provide('weight_mode', weight_mode)
provide('charge_mode', charge_mode)

const {
  tableState: { pageSize, currentPage, total, loading, dataList },
  tableMethods,
  formData
} = useTable<FirstVesselSettingEntity>({
  immediate: true,
  initialFormData: {
    transportServiceName: undefined,
    transportServiceCode: undefined,
    chargeMode: undefined,
    weightMode: undefined,
    arrivalCountry: undefined,
    destinationType: ''
  },
  fetchDataApi: async () => {
    const res = await getFirstVesselSettingList({
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      ...formData
    })
    return {
      list: res.result.records,
      total: res.result.total
    }
  },
  fetchDelApi: async (record: any) => {
    if (Array.isArray(record)) {
      const ids = record.map((item: any) => item.id?.toString())
      const res = await deleteFirstVesselSetting({ ids })
      return !!res
    } else {
      const res = await deleteFirstVesselSetting({ ids: [record.id] })
      return !!res
    }
  }
})

const tableRef = ref()

const active = ref('1')

const [awVisible, handleAw] = useToggle()
const [acVisible, handleAc] = useToggle()
const [accVisible, handleAcc] = useToggle()
</script>

<template>
  <ContentWrap>
    <el-form
      :model="formData"
      ref="formDataRef"
      label-position="left"
      @submit.prevent
    >
      <el-row :gutter="16">
        <el-col :span="8">
          <el-form-item label="渠道类型" prop="">
            <DSelect
              v-model="formData.chargeMode"
              :options="charge_mode"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="物流渠道代码" prop="">
            <el-input
              v-model.trim="formData.chargeMode"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="物流渠道名称" prop="">
            <el-input
              v-model.trim="formData.chargeMode"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="承运商" prop="">
            <DSelect
              v-model="formData.weightMode"
              :options="weight_mode"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="保险服务" prop="">
            <el-radio-group v-model="formData.name">
              <el-radio :label="'支持保险'" :value="1"></el-radio>
              <el-radio :label="'不支持保险'" :value="2"></el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="签名服务" prop="">
            <el-checkbox-group v-model="formData.name">
              <el-checkbox label="直接签名" :value="1" />
              <el-checkbox label="间接签名" :value="2" />
              <el-checkbox label="成人签名" :value="3" />
            </el-checkbox-group>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="上传物流面单" prop="">
            <el-radio-group v-model="formData.name">
              <el-radio :label="'不需要'" :value="1"></el-radio>
              <el-radio :label="'需要'" :value="2"></el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col :span="8" v-if="formData.name === 2">
          <el-form-item label="平台面单" prop="">
            <el-radio-group v-model="formData.name">
              <el-radio :label="'获取'" :value="1"></el-radio>
              <el-radio :label="'不获取'" :value="2"></el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col :span="8" v-if="formData.name === 2">
          <el-form-item label="客户上传面单" prop="">
            <el-radio-group v-model="formData.name">
              <el-radio :label="'支持'" :value="1"></el-radio>
              <el-radio :label="'不支持'" :value="2"></el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ContentWrap>

  <ContentWrap class="mt-20">
    <el-tabs
      v-model="active"
      class="mb-16"
      @tab-click="tableMethods.handleQuery"
    >
      <el-tab-pane label="绑定仓库" name="1">
        <div class="mb-18">
          <el-button @click="() => handleAw()">添加仓库</el-button>
        </div>
        <el-table
          v-loading="loading"
          ref="tableRef"
          :data="dataList"
          :border="true"
          class="w-full"
          @selection-change="val => tableMethods.handleSelectionChange(val)"
        >
          <!-- <el-table-column type="selection" fixed="left" width="55" /> -->
          <el-table-column label="仓库代码" prop="transportServiceName" />
          <el-table-column label="仓库名称" prop="transportServiceCode" />
          <el-table-column label="结算币种" prop="chargeModeName" width="180" />
          <el-table-column label="状态" prop="weightModeName" width="180" />

          <el-table-column label="操作" fixed="right" width="260">
            <template #default="{}">
              <el-button text type="danger">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, ->, sizes, prev, pager, next, jumper"
          :total="total"
          class="mt-16"
        />
      </el-tab-pane>
      <el-tab-pane label="绑定渠道" name="2">
        <div class="mb-18">
          <el-button @click="() => handleAc()">添加渠道</el-button>
        </div>
        <el-table
          v-loading="loading"
          ref="tableRef"
          :data="dataList"
          :border="true"
          class="w-full"
          @selection-change="val => tableMethods.handleSelectionChange(val)"
        >
          <!-- <el-table-column type="selection" fixed="left" width="55" /> -->
          <el-table-column label="系统渠道代码" prop="transportServiceCode" />
          <el-table-column label="系统渠道名称" prop="transportServiceName" />

          <el-table-column label="状态" prop="chargeModeName" width="180" />
          <el-table-column label="绑定仓库" prop="weightModeName" />
          <el-table-column
            label="保险服务"
            prop="bubbleCoefficient"
            width="180"
          />
          <el-table-column label="签名服务" prop="arrivalCountry" width="180" />

          <el-table-column label="操作" fixed="right" width="260">
            <template #default="{}">
              <el-button text type="danger">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, ->, sizes, prev, pager, next, jumper"
          :total="total"
          class="mt-16"
        />
      </el-tab-pane>
      <el-tab-pane label="绑定客户" name="3">
        <div class="mb-18">
          <el-button @click="() => handleAcc()">绑定客户</el-button>
        </div>
        <el-table
          v-loading="loading"
          ref="tableRef"
          :data="dataList"
          :border="true"
          class="w-full"
          @selection-change="val => tableMethods.handleSelectionChange(val)"
        >
          <!-- <el-table-column type="selection" fixed="left" width="55" /> -->
          <el-table-column label="客户" prop="transportServiceName" />
          <el-table-column
            label="类型"
            prop="transportServiceCode"
            width="180"
          />

          <el-table-column label="操作" fixed="right" width="260">
            <template #default="{}">
              <el-button text type="danger">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, ->, sizes, prev, pager, next, jumper"
          :total="total"
          class="mt-16"
        />
      </el-tab-pane>
    </el-tabs>
  </ContentWrap>

  <AddWarehouse v-model="awVisible"></AddWarehouse>
  <AddChannel v-model="acVisible"></AddChannel>
  <AddCustomer v-model="accVisible"></AddCustomer>
</template>

<style lang="scss" scoped></style>
