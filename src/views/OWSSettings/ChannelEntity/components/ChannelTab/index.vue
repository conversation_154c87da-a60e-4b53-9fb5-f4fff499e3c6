<script setup lang="ts">
import { useTable } from '@/hooks/useTable'
import {
  deleteFirstVesselSetting,
  getFirstVesselSettingList
} from '@/api/initialServiceSettings'
import type { FirstVesselSettingEntity } from '@/api/initialServiceSettings/types'
import { useToggle } from '@/hooks/useToggle'
import AddChannel from './AddChannel.vue'

defineOptions({
  name: 'ChannelTab'
})

const {
  tableState: { pageSize, currentPage, total, loading, dataList },
  tableMethods
} = useTable<FirstVesselSettingEntity>({
  immediate: true,
  initialFormData: {
    transportServiceName: undefined,
    transportServiceCode: undefined,
    chargeMode: undefined,
    weightMode: undefined,
    arrivalCountry: undefined,
    destinationType: ''
  },
  fetchDataApi: async () => {
    const res = await getFirstVesselSettingList({
      pageSize: pageSize.value,
      pageNum: currentPage.value
    })
    return {
      list: res.result.records,
      total: res.result.total
    }
  },
  fetchDelApi: async (record: any) => {
    if (Array.isArray(record)) {
      const ids = record.map((item: any) => item.id?.toString())
      const res = await deleteFirstVesselSetting({ ids })
      return !!res
    } else {
      const res = await deleteFirstVesselSetting({ ids: [record.id] })
      return !!res
    }
  }
})

const tableRef = ref()
const [acVisible, handleAc] = useToggle()
</script>

<template>
  <div>
    <div class="mb-18">
      <el-button @click="() => handleAc()">添加渠道</el-button>
    </div>
    <el-table
      v-loading="loading"
      ref="tableRef"
      :data="dataList"
      :border="true"
      class="w-full"
      @selection-change="val => tableMethods.handleSelectionChange(val)"
    >
      <el-table-column label="系统渠道代码" prop="transportServiceCode" />
      <el-table-column label="系统渠道名称" prop="transportServiceName" />
      <el-table-column label="状态" prop="chargeModeName" width="180" />
      <el-table-column label="绑定仓库" prop="weightModeName" />
      <el-table-column
        label="保险服务"
        prop="bubbleCoefficient"
        width="180"
      />
      <el-table-column label="签名服务" prop="arrivalCountry" width="180" />

      <el-table-column label="操作" fixed="right" width="260">
        <template #default="{}">
          <el-button text type="danger">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, ->, sizes, prev, pager, next, jumper"
      :total="total"
      class="mt-16"
    />

    <AddChannel v-model="acVisible" />
  </div>
</template>

<style lang="scss" scoped></style>
