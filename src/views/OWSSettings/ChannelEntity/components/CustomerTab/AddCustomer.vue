<script setup lang="ts">
defineOptions({
  name: 'AddCustomer'
})

const visible = defineModel({ default: false })

// const {} = defineProps<{}>()

const formData = ref({
  name: '',
  num: 0
})

const tableData = ref([
  {
    date: '2016-05-02',
    name: '王小虎',
    address: '上海市普陀区金沙江路 1518 弄'
  }
])

const handleOpen = () => {
  visible.value = true
}
const handleClose = () => {
  visible.value = false
}
const handleCancel = () => {
  visible.value = false
}
const handleConfirn = () => {
  visible.value = false
}
</script>

<template>
  <el-dialog
    v-model="visible"
    title="绑定客户"
    width="70%"
    @open="handleOpen"
    @close="handleClose"
  >
    <div class="flex justify-between mb-16">
      <div class="flex items-center">
        <el-input
          v-model.trim="formData.name"
          clearable
          placeholder=""
        ></el-input>
        <el-button type="primary" class="ml-16">搜索</el-button>

        <div class="!w-210 ml-12">
          <el-radio-group v-model="formData.name" class="w-210">
            <el-radio :label="'可用客户'" :value="1"></el-radio>
            <el-radio :label="'不可用客户'" :value="2"></el-radio>
          </el-radio-group>
        </div>
      </div>

      <el-popover placement="bottom-start" :width="400" trigger="click">
        <template #reference>
          <el-button>已选84个客户</el-button>
        </template>
        <el-table :data="tableData" :border="true" style="width: 100%">
          <el-table-column label="客户代码" prop="name" />
          <el-table-column label="客户名称" prop="name" />
        </el-table>
      </el-popover>
    </div>
    <el-table :data="tableData" :border="true" style="width: 100%">
      <el-table-column type="selection" width="60" />
      <el-table-column label="客户代码" prop="name" />
      <el-table-column label="客户名称" prop="name" />
    </el-table>

    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirn"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
