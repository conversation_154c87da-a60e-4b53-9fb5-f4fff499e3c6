<script setup lang="ts">
defineOptions({
  name: 'AddChannel'
})

const visible = defineModel({ default: false })

// const {} = defineProps<{}>()

const formData = ref({
  name: '',
  num: 0
})

const tableData = ref([
  {
    date: '2016-05-02',
    name: '王小虎',
    address: '上海市普陀区金沙江路 1518 弄'
  }
])

const handleOpen = () => {
  visible.value = true
}
const handleClose = () => {
  visible.value = false
}
const handleCancel = () => {
  visible.value = false
}
const handleConfirn = () => {
  visible.value = false
}
</script>

<template>
  <el-dialog
    v-model="visible"
    title="绑定渠道"
    width="70%"
    @open="handleOpen"
    @close="handleClose"
  >
    <div class="flex justify-between mb-16">
      <div class="flex items-center">
        <el-select
          v-model="formData.name"
          clearable
          placeholder=""
          class="!w-150"
        >
          <el-option :value="1" label="仓库"></el-option>
        </el-select>
        <el-input
          v-model.trim="formData.name"
          clearable
          placeholder=""
        ></el-input>
        <el-button type="primary" class="ml-16">搜索</el-button>
      </div>

      <el-popover placement="bottom-start" :width="400" trigger="click">
        <template #reference>
          <el-button>已选84个渠道</el-button>
        </template>
        <el-table :data="tableData" :border="true" style="width: 100%">
          <el-table-column label="系统渠道代码" prop="name" />
          <el-table-column label="系统渠道名称" prop="name" />
          <el-table-column label="绑定仓库" prop="name" />
          <el-table-column label="保险服务" prop="name" />
          <el-table-column label="签名服务" prop="name" />
        </el-table>
      </el-popover>
    </div>
    <el-table :data="tableData" :border="true" style="width: 100%">
      <el-table-column type="selection" width="60" />
      <el-table-column label="系统渠道代码" prop="name" />
      <el-table-column label="系统渠道名称" prop="name" />
      <el-table-column label="绑定仓库" prop="name">
        <template #default="{ row }">
          <el-popover placement="bottom-start" :width="400" trigger="click">
            <template #reference>
              <div>{{ row.name }}</div>
            </template>
            <el-table :data="tableData" :border="true" style="width: 100%">
              <el-table-column label="仓库名称" prop="name" />
              <el-table-column label="仓库代码" prop="name" />
              <el-table-column label="状态" prop="name" />
            </el-table>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="保险服务" prop="name" />
      <el-table-column label="签名服务" prop="name" />
    </el-table>

    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirn"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
