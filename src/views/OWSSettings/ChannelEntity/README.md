# ChannelEntity 组件重构说明

## 重构概述

将原来的单一文件 `index.vue` 中的三个 tab 拆分成独立的组件，每个 tab 对应一个组件目录，并将对应的弹窗组件移动到相应的目录下。

## 重构前后对比

### 重构前
```
src/views/OWSSettings/ChannelEntity/
├── index.vue                    # 包含所有 tab 逻辑的主文件
└── components/
    ├── AddWarehouse.vue         # 添加仓库弹窗
    ├── AddChannel.vue           # 添加渠道弹窗
    └── AddCustomer.vue          # 添加客户弹窗
```

### 重构后
```
src/views/OWSSettings/ChannelEntity/
├── index.vue                    # 简化的主文件，只负责 tab 切换
└── components/
    ├── WarehouseTab/
    │   ├── index.vue            # 绑定仓库 tab 组件
    │   └── AddWarehouse.vue     # 添加仓库弹窗
    ├── ChannelTab/
    │   ├── index.vue            # 绑定渠道 tab 组件
    │   └── AddChannel.vue       # 添加渠道弹窗
    └── CustomerTab/
        ├── index.vue            # 绑定客户 tab 组件
        └── AddCustomer.vue      # 添加客户弹窗
```

## 重构内容

### 1. 主文件 (index.vue)
- 移除了复杂的表格逻辑和数据处理
- 保留了字典数据的 provide 逻辑
- 简化为只负责 tab 切换和组件渲染
- 保留了原有的表单搜索功能

### 2. WarehouseTab 组件
- **文件位置**: `components/WarehouseTab/index.vue`
- **功能**: 绑定仓库的表格展示和操作
- **包含弹窗**: `AddWarehouse.vue`
- **表格字段**: 仓库代码、仓库名称、结算币种、状态

### 3. ChannelTab 组件
- **文件位置**: `components/ChannelTab/index.vue`
- **功能**: 绑定渠道的表格展示和操作
- **包含弹窗**: `AddChannel.vue`
- **表格字段**: 系统渠道代码、系统渠道名称、状态、绑定仓库、保险服务、签名服务

### 4. CustomerTab 组件
- **文件位置**: `components/CustomerTab/index.vue`
- **功能**: 绑定客户的表格展示和操作
- **包含弹窗**: `AddCustomer.vue`
- **表格字段**: 客户、类型

## 技术实现

### 数据管理
- 每个 tab 组件都有独立的 `useTable` hook
- 使用相同的 API 接口 (`getFirstVesselSettingList`, `deleteFirstVesselSetting`)
- 每个组件都有独立的分页和加载状态

### 弹窗管理
- 每个 tab 组件内部管理自己的弹窗状态
- 使用 `useToggle` hook 管理弹窗的显示/隐藏
- 弹窗组件使用 `defineModel` 进行双向绑定

### 样式和布局
- 保持原有的表格样式和分页组件
- 使用 UnoCSS 原子化类进行样式控制
- 每个组件都有独立的 scoped 样式

## 优势

1. **代码组织更清晰**: 每个 tab 的逻辑独立，便于维护
2. **组件复用性更好**: 每个 tab 组件可以独立使用
3. **开发效率提升**: 多人可以并行开发不同的 tab
4. **测试更容易**: 可以针对每个 tab 组件单独编写测试
5. **代码可读性更强**: 单个文件的代码量减少，逻辑更集中

## 注意事项

1. 所有组件都依赖父组件提供的字典数据 (`warehouse_type`, `service_provider_type`, `weight_mode`, `charge_mode`)
2. 每个 tab 组件都有独立的数据加载和状态管理
3. 弹窗组件的路径已更新，确保导入路径正确
4. 保持了原有的 API 接口和数据结构，不影响后端交互
